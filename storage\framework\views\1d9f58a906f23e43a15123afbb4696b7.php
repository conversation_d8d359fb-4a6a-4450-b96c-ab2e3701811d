<?php

$inv_tax = 0;
$inv_discount = 0;
$inv_adjustment = 0;

//get invoice info
$inv_id = $invoice->id;

//compute terms
$inv_total = number_format($invoice->total_amount, 2);

$cashier = $invoice->updated_by;
$customer_name=$order->customer_name;
$inv_date = $invoice->updated_at;

$inv_details = '
<div class="details">
    <div class="">Invoice#: '.$inv_id.'</div>
    <div class="">Date: '.$inv_date.'</div>
    <div class="">Cashier: '.$cashier.'</div>
    <div class="">Customer: '.$customer_name.'</div>
</div>';

$inv_header = '
<div class="header">
    <div class="">'.$setting->where('key', 'site_name')->value('value').'</div>
    <div class="">'.$setting->where('key', 'address')->value('value').'</div>
    <div class="">Phone:'.$setting->where('key', 'phone')->value('value').'</div>
    <div class="">TIN:'.$setting->where('key', 'tin')->value('value').'</div>
</div>';


$inv_goods = '<div class="inv-goods"><table width="100%">
<tbody>';

foreach ($orderItems as $item) {

    $commodity_name = $item->menu->name;
    $quantities = $item['quantity'];
    $qty = number_format($quantities, 1);
    $unit_price = $item['price'];
    $amount = number_format($unit_price * $quantities, 2);
    $inv_goods .= '<tr>';
    $inv_goods .= '<td><div class="product-data">' . $commodity_name .' '. $qty . 'x' .$unit_price. '</div></td><td><div class="pull-right">'.$amount.'</div></td>';
    $inv_goods .= '</tr>';
}
//close goods table
$inv_goods .= '<tr><div class="product-data"></div><td></td><td><div class="pull-right">------</div></td></tr>';
$inv_goods .= '<tr><td>Total:</td><td><div class="pull-right">'.$inv_total.'</div></td></tr></tbody>';
$inv_goods .= '</table></div>';

$inv_footer = '
<div class="">
    <div>Status:'.$invoice->status.'</div><br>
    <div>Thank you for your patronage. Pls come again!</div>
</div>
';

$html = '<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN"
           "http://www.w3.org/TR/html4/strict.dtd">
        <html>
           <heade>
              <TITLE>POS Invoice</TITLE>
           ';
           
//init styles
$html .= '<style>
        @media print {
            .dr-pagebreak { page-break-after: always;}
            .no-print { display: none; }
            
        }
        body {
            width: 58mm; /*58 or 80mm */
            margin: 0; /* Remove default margins */
        }
        .inv-goods {
            margin-bottom: 10px;
        }

        .container {
            margin-left:0px;
            font-size: 12px; /* Adjust font size as needed */
            line-height: 1.2; /* Line spacing to fit more content */
        }
        div {
            font-family: Courier New;
        }
        table {
            border-collapse: collapse;
        }

        .header {
            margin-bottom: 10px;
            text-align:center;
        }

        .details {
            margin-bottom: 10px;
        }

        .product-data {
            overflow-y:hidden;
        }
        .pull-right {
            float:right;
        }

        </style>
        </heade>
           <body>
           ';//white space no wrap prevents contents from spilling into 2nd line

//assemble the page
$backtoPOS = '
<div class="no-print" style="text-align:center;margin-top:10px;margin-bottom:10px;">
    <a class="" style="padding:3px;text-decoration:none;background:#36b9cc; color:#FFF;border-radius:3px;" href="'.route('admin.order.add').'">Back to POS</a>
</div>
';

$html .= $backtoPOS;
$html .= '<div class="container">';
$html .= $inv_header;
$html .= $inv_details;
$html .= $inv_goods;
$html .= $inv_footer;
$html .= '</div>';
    
$html .= '<script>window
    .addEventListener("load",
        function () {
           window.print()
        });</script>';	//javascript
$html .= '</body>
        </html>';
echo $html;
?><?php /**PATH C:\Users\<USER>\Documents\GitHub\hotel_management_system\resources\views\admin\invoices\print-posinv.blade.php ENDPATH**/ ?>