<?php echo $__env->make('layout.admin-header', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<?php
    $currencySymbol = config('currency.symbol');
?>

<body id="page-top">

    <!-- Page Wrapper -->
    <div id="wrapper">

        <!-- Sidebar -->
        <?php echo $__env->make('layout.admin-sidebar', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        <!-- End of Sidebar -->

        <!-- Content Wrapper -->
        <div id="content-wrapper" class="d-flex flex-column">

            <!-- Main Content -->
            <div id="content">

                <!-- Topbar -->
                <?php echo $__env->make('layout.admin-topbar', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                <!-- End of Topbar -->

                <!-- Begin Page Content -->
                <div class="container-fluid px-4">
                    <h4 class="mt-4">Grocery List</h4>
                    <?php echo $__env->make('shared.error-message', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                    <?php echo $__env->make('shared.success-message', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                    <?php session()->forget('error'); /*vince added*/ ?>

                    <div class="row">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-header">
                                    <h6>Select Date Range</h6>
                                </div>
                                <div class="card-body">
                                    <form action="<?php echo e(route('grocery.search')); ?>" method="GET">
                                        <div class="row">
                                            <input type="hidden" name="start_date" id="start_date">
                                            <input type="hidden" name="end_date" id="end_date">
                                            <div class="col-md-5">
                                                <label for="start_date">Start Date</label>
                                                <input type="text" class="form-control datepicker1" required
                                                    autocomplete="off">
                                            </div>
                                            <div class="col-md-5">
                                                <label for="end_date">End Date</label>
                                                <input type="text" class="form-control datepicker2" required
                                                    autocomplete="off">
                                            </div>
                                            <button type="submit" class="btn btn-primary mt-3">Search</button>
                                        </div>
                                        
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>

                    
                    <?php if(isset($groceries) && is_array($groceries) && count($groceries) > 0): ?>
                        <div class="row mt-4">
                            <div class="col-md-12">
                                
                                    
                                        <h4>Groceries for
                                            
                                            <?php echo e(\Carbon\Carbon::parse(Request::get('start_date'))->format('F j, Y')); ?> -
                                            <?php echo e(\Carbon\Carbon::parse(Request::get('end_date'))->format('F j, Y')); ?>

                                        </h4>
                                    
                                    <table class="table table-bordered table-striped" id="myRequests2">
                                        <thead>
                                            <tr>
                                                <th>For: Event ID</th>
                                                <th>Ingredient Name</th>
                                                <th>Quantity</th>
                                                <th>Supplier</th>
                                                <th>Cost</th>
                                                <th>Purchased?</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php $__currentLoopData = $groceries; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $grocery): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <tr>
                                                    <td><?php if (!empty($grocery['event_ids'])): ?>
                                                        <?= implode(', ', $grocery['event_ids']); ?>
                                                    <?php endif; ?>
                                                        
                                                    </td>
                                                    <td><?php echo e($grocery['ingredient_name']); ?></td>
                                                    <td><?php echo e($grocery['quantity'] . ' ' . $grocery['unit']); ?></td>
                                                    <td><?php echo e($grocery['supplier']); ?></td>
                                                    <td><?php echo e($currencySymbol . number_format($grocery['cost'], 2)); ?></td>
                                                    <td>
                                                        <input type="checkbox" class="grocery-checkbox"
                                                            data-ingredient-id="<?php echo e($grocery['ingredient_id']); ?>"
                                                            data-event-ids="<?php echo e(json_encode($grocery['event_ids'])); ?>"
                                                            <?php echo e($grocery['is_bought'] ? 'checked' : ''); ?>>

                                                    </td> <!-- Checkbox for purchased status -->
                                                </tr>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </tbody>
                                        
                                    </table>
                                    <div class="text-right"><h5>Total Cost: 
                                        <span><?php echo e($currencySymbol . number_format($totalCost, 2)); ?></span></h5>
                                    </div>
                                
                            </div>
                        </div>
                    <?php endif; ?>

                </div>
                <!-- /.container-fluid -->

            </div>
            <!-- End of Main Content -->

            <!-- Footer -->
            <?php echo $__env->make('layout.admin-footer', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
            <!-- End of Footer -->

        </div>
        <!-- End of Content Wrapper -->

    </div>
    <!-- End of Page Wrapper -->

    <!-- Scroll to Top Button-->
    <a class="scroll-to-top rounded" href="#page-top">
        <i class="fas fa-angle-up"></i>
    </a>

    <!-- Logout Modal-->
    <div class="modal fade" id="logoutModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel"
        aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="exampleModalLabel">Ready to Leave?</h5>
                    <button class="close" type="button" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">×</span>
                    </button>
                </div>
                <div class="modal-body">Select "Logout" below if you are ready to end your current session.</div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" type="button" data-dismiss="modal">Cancel</button>
                    <a class="btn btn-primary" href="login.html">Logout</a>
                </div>
            </div>
        </div>
    </div>

    <?php echo $__env->make('layout.scripts', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

</body>

<script>
    $(document).ready(function() {
        $('.datepicker1').datepicker({
            showOnFocus: true,
            dateFormat: 'mm/dd/yy', // Displayed date format
            altFormat: 'yy-mm-dd', // Format for form submission
            altField: "#start_date" // IDs of hidden input fields to store formatted dates
        });
        <?php if (!isset($groceries)): /*vince added block*/ ?>
            $('.datepicker1').datepicker("setDate", new Date());
        <?php endif; ?>
        $('.datepicker2').datepicker({
            showOnFocus: true,
            dateFormat: 'mm/dd/yy', // Displayed date format
            altFormat: 'yy-mm-dd', // Format for form submission
            altField: "#end_date" // IDs of hidden input fields to store formatted dates
        });
        <?php if (!isset($groceries)): /*vince added block*/ ?>
            $('.datepicker2').datepicker("setDate", new Date());
        <?php endif; ?>
    });

    $(document).ready(function() {
        $('.grocery-checkbox').on('change', function() {
            const ingredientId = $(this).data('ingredient-id');
            const eventIds = $(this).data('event-ids'); // Updated to handle an array of event IDs
            const isChecked = $(this).is(':checked') ? 1 : 0;

            $.ajax({
                url: '<?php echo e(route('grocery.update-purchase-status')); ?>',
                method: 'POST',
                data: {
                    _token: '<?php echo e(csrf_token()); ?>',
                    ingredient_id: ingredientId,
                    event_ids: eventIds, // Send the array of event IDs
                    is_bought: isChecked
                },
                success: function(response) {
                    if (response.success) {
                        alert('Purchase status updated successfully!');
                    } else {
                        alert('An error occurred. Please try again.');
                    }
                },
                error: function(xhr) {
                    alert('An error occurred: ' + xhr.responseText);
                }
            });
        });
    });
</script>


</html>
<?php /**PATH C:\Users\<USER>\Documents\GitHub\hotel_management_system\resources\views\admin\grocery\grocery-view.blade.php ENDPATH**/ ?>