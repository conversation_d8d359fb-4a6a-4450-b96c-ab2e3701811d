<?php echo $__env->make('layout.admin-header', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

<body id="page-top">

    <!-- Page Wrapper -->
    <div id="wrapper">

        <!-- Sidebar -->
        <?php echo $__env->make('layout.admin-sidebar', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        <!-- End of Sidebar -->

        <!-- Content Wrapper -->
        <div id="content-wrapper" class="d-flex flex-column">

            <!-- Main Content -->
            <div id="content">

                <!-- Topbar -->
                <?php echo $__env->make('layout.admin-topbar', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                <!-- End of Topbar -->

                <!-- Begin Page Content -->
                <div class="container-fluid">
                    <?php echo $__env->make('shared.success-message', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                    <?php echo $__env->make('shared.error-message', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">Bookings</h6>
                            <a href="<?php echo e(route('admin.booking.add')); ?>" class="btn btn-primary float-right"><i class="fa fa-plus"></i> Add Booking</a>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table id="myRequests" class="table table-bordered table-striped" width="100%"
                                    cellspacing="0">
                                    <thead>
                                        <tr>
                                            <th>Book ID</th>
                                            <th>Guest Name</th>
                                            <th>Room Number</th>
                                            <th>Room Type</th>
                                            <th>Check In Date</th>
                                            <th>Check Out Date</th>
                                            <th>Booked By</th>
                                            <th>Status</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <!-- Moment -->
                                    <script src="<?php echo e(asset('assets/js/moment.min.js')); ?>"></script>
                                    <tbody>
                                        <?php $__currentLoopData = $bookings; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $booking): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <tr>
                                                <td><?php echo e($booking->id); ?></td>
                                                <td><?php if($booking->guest_id): ?>
                                                    <a href="<?php echo e(route('admin.guest.edit', $booking->guest_id)); ?>">
                                                        <?php echo e($booking->guest_first_name . ' ' . $booking->guest_last_name); ?>

                                                    </a>
                                                    <?php else: ?>
                                                        <?php echo e($booking->guest_first_name . ' ' . $booking->guest_last_name); ?>

                                                    <?php endif; ?>
                                                </td>
                                                <td><?php echo e($booking->room->room_number); ?></td>
                                                <td><?php echo e($booking->room->roomType->name); ?></td>
                                                <td class="checkin-date" data-date="<?php echo e($booking->checkin_date); ?>">
                                                    <?php echo e($booking->checkin_date); ?></td>
                                                <td class="checkout-date" data-date="<?php echo e($booking->checkout_date); ?>">
                                                    <?php echo e($booking->checkout_date); ?></td>
                                                <td><?php echo e($booking->booked_by); ?></td>
                                                <td><?php echo e($booking->booking_status); ?></td>

                                                <td>
                                                    <a href="<?php echo e(route('admin.booking.edit', $booking->id)); ?>"
                                                        class="btn btn-primary btn-circle btn-sm" title="Edit Booking">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <form action="<?php echo e(route('admin.booking.destroy', $booking->id)); ?>"
                                                        method="POST" style="display: inline;">
                                                        <?php echo csrf_field(); ?>
                                                        <?php echo method_field('DELETE'); ?>
                                                        <button type="submit" class="btn btn-danger btn-circle btn-sm" title="Delete Booking">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </form>

                                                    <!-- Edit Invoice Button -->
                                                    <a href="<?php echo e(route('admin.invoice.edit',  $booking->invoices->first()->id)); ?>"
                                                        class="btn btn-info btn-circle btn-sm" title="Edit Invoice">
                                                        <i class="fas fa-file-invoice"></i>
                                                    </a>
                                                </td>
                                            </tr>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </tbody>
                                </table>
                                <script>
                                    document.addEventListener('DOMContentLoaded', function() {
                                        // Function to format dates using moment.js
                                        function formatDate(dateString) {
                                            return moment(dateString).format('MMMM D, YYYY h:mm A');
                                        }

                                        // Format all check-in dates
                                        document.querySelectorAll('.checkin-date').forEach(function(element) {
                                            const date = element.getAttribute('data-date');
                                            element.textContent = formatDate(date);
                                        });

                                        // Format all check-out dates
                                        document.querySelectorAll('.checkout-date').forEach(function(element) {
                                            const date = element.getAttribute('data-date');
                                            element.textContent = formatDate(date);
                                        });
                                    });
                                </script>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- /.container-fluid -->

            </div>
            <!-- End of Main Content -->

            <!-- Footer -->
            <?php echo $__env->make('layout.admin-footer', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
            <!-- End of Footer -->

        </div>
        <!-- End of Content Wrapper -->

    </div>
    <!-- End of Page Wrapper -->

    <!-- Scroll to Top Button-->
    <a class="scroll-to-top rounded" href="#page-top">
        <i class="fas fa-angle-up"></i>
    </a>

    <!-- Logout Modal-->
    <div class="modal fade" id="logoutModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel"
        aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="exampleModalLabel">Ready to Leave?</h5>
                    <button class="close" type="button" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">×</span>
                    </button>
                </div>
                <div class="modal-body">Select "Logout" below if you are ready to end your current session.</div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" type="button" data-dismiss="modal">Cancel</button>
                    <a class="btn btn-primary" href="login.html">Logout</a>
                </div>
            </div>
        </div>
    </div>

    <?php echo $__env->make('layout.scripts', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

</body>

</html>
<?php /**PATH C:\Users\<USER>\Documents\GitHub\hotel_management_system\resources\views\admin\bookings\view-bookings.blade.php ENDPATH**/ ?>