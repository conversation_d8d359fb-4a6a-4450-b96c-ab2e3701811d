<?php echo $__env->make('layout.admin-header', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

<body id="page-top">

    <!-- Page Wrapper -->
    <div id="wrapper">

        <!-- Sidebar -->
        <?php echo $__env->make('layout.admin-sidebar', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        <!-- End of Sidebar -->

        <!-- Content Wrapper -->
        <div id="content-wrapper" class="d-flex flex-column">

            <!-- Main Content -->
            <div id="content">

                <!-- Topbar -->
                <?php echo $__env->make('layout.admin-topbar', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                <!-- End of Topbar -->

                <!-- Begin Page Content -->
                <div class="container-fluid">
                    <?php echo $__env->make('shared.success-message', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                    <?php echo $__env->make('shared.error-message', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">Edit Invoice for Booking
                                #<?php echo e(isset($invoice->booking->id) ? $invoice->booking->id: 'N/A'); ?> - <?php echo e($invoice->booking->guest_first_name); ?> <?php echo e($invoice->booking->guest_last_name); ?></h6>
                                <a href="<?php echo e(route('admin.invoices')); ?>" class="btn btn-danger float-right">BACK</a>
                        </div>
                        <div class="card-body">
                            <!-- Invoice Edit Form -->
                            <form action="<?php echo e(route('admin.invoice.update', $invoice->id)); ?>" method="POST">
                                <?php echo csrf_field(); ?>
                                <?php echo method_field('PATCH'); ?>
                                <div class="row">

                                    <!-- Booking Details -->
                                    <div class="form-group col-md-2">
                                        <label for="booking_id">Booking ID</label>
                                        <input type="text" id="booking_id" name="booking_id" class="form-control"
                                            value="<?php echo e($invoice->booking->id); ?>" readonly>
                                    </div>
                                    <div class="form-group col-md-4">
                                        <label for="room_details">Room Details</label>
                                        <input type="text" id="room_details" name="room_details" class="form-control"
                                            value="<?php echo e($invoice->booking->room->room_number); ?> - <?php echo e($invoice->booking->room->roomType->name); ?>"
                                            readonly>
                                    </div>


                                    <div class="form-group col-md-3">
                                        <label for="checkin_date">Check-in Date</label>
                                        <input type="text" id="checkin_date" name="checkin_date" class="form-control"
                                        value="<?php echo e(\Carbon\Carbon::parse($invoice->booking->checkin_date)->format('Y-m-d')); ?>"

                                            readonly>
                                    </div>

                                    <div class="form-group col-md-3">
                                        <label for="checkout_date">Check-out Date</label>
                                        <input type="text" id="checkout_date" name="checkout_date"
                                            class="form-control"
                                            value="<?php echo e(\Carbon\Carbon::parse($invoice->booking->checkout_date)->format('Y-m-d')); ?>"

                                            readonly>
                                    </div>

                                    <div class="form-group col-md-6">
                                        <label for="guest_name">Guest Name</label>
                                        <input type="text" id="guest_name" name="guest_name" class="form-control"
                                            value="<?php echo e($invoice->booking->guest_first_name); ?> <?php echo e($invoice->booking->guest_last_name); ?>"
                                            readonly>
                                    </div>

                                    
                                    <div class="form-group col-md-3">
                                        <label for="total_price">Sub Total</label>
                                        <input type="text" id="total_price" name="total_price"  class="form-control text-right" value="<?php echo e($invoice->booking->total_price); ?>" readonly>
                                            
                                    </div>

                                    <!-- Total Price -->
                                    <div class="form-group col-md-3">
                                        <label for="total_amount">Total Amount</label>
                                        <input type="text" id="total_amount" name="total_amount" class="form-control text-right" style="font-size:1.2em;"
                                            readonly>
                                    </div>
                                    <div class="form-group col-md-3">
                                        <label for="guest_phone">Phone</label>
                                        <input type="text" class="form-control" id="guest_phone" name="guest_phone"
                                            value="<?php echo e($invoice->booking->guest_phone); ?>" readonly>
                                    </div>
                                    <div class="form-group col-md-3">
                                        <label for="guest_email">Email</label>
                                        <input type="text" class="form-control" id="guest_email" name="guest_email"
                                        value="<?php echo e($invoice->booking->guest_email); ?>" readonly>
                                    </div>

                                    <div class="form-group col-md-12">
                                        <button type="button" id="add-charge-btn" class="btn btn-secondary">Additional Charge</button>
                                    </div>
                                    <!-- Additional Charges Container -->
                                    <div class="form-group col-md-12" id="additional-charges-container">
                                        <!-- Existing Charges -->
                                        <?php $__currentLoopData = $invoice->additionalCharges; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $charge): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <div class="form-group row additional-charge-row">
                                                <div class="col-md-6">
                                                    <label for="additional_charge_description">Description</label>
                                                    <input type="text" id="additional_charge_description"
                                                        name="additional_charge_description[]" class="form-control"
                                                        value="<?php echo e($charge->description); ?>"
                                                        placeholder="Description of charge">
                                                </div>
                                                <div class="col-md-3">
                                                    <label for="additional_charge_amount">Amount</label>
                                                    <input type="number" id="additional_charge_amount"
                                                        name="additional_charge_amount[]" class="form-control text-right"
                                                        value="<?php echo e($charge->amount); ?>" placeholder="Amount">
                                                </div>
                                                <div class="col-md-3 d-flex align-items-end">
                                                    <button type="button" title="Remove"
                                                        class="btn btn-danger remove-charge-btn"><i class="fa fa-trash"></i></button>
                                                </div>
                                            </div>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        <!-- JavaScript will append additional charge fields here -->
                                    </div>
                           

                                    
                                    <input type="hidden" name="updated_by" value="<?php echo e(get_user_email()); ?>">
                                    
                                </div>

                                <div class="form-group col-md-12 text-center">
                                    <button type="submit" class="btn btn-primary">Update Invoice</button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
                <!-- /.container-fluid -->

            </div>
            <!-- End of Main Content -->

            <!-- Footer -->
            <?php echo $__env->make('layout.admin-footer', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
            <!-- End of Footer -->

        </div>
        <!-- End of Content Wrapper -->

    </div>
    <!-- End of Page Wrapper -->

    <!-- Scroll to Top Button-->
    <a class="scroll-to-top rounded" href="#page-top">
        <i class="fas fa-angle-up"></i>
    </a>

    <!-- Logout Modal-->
    <div class="modal fade" id="logoutModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel"
        aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="exampleModalLabel">Ready to Leave?</h5>
                    <button class="close" type="button" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">×</span>
                    </button>
                </div>
                <div class="modal-body">Select "Logout" below if you are ready to end your current session.</div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" type="button" data-dismiss="modal">Cancel</button>
                    <a class="btn btn-primary" href="login.html">Logout</a>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap core JavaScript-->
    <script src="<?php echo e(asset('assets/js/jquery.min.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/js/bootstrap.bundle.min.js')); ?>"></script>

    <!-- Core plugin JavaScript-->
    <script src="<?php echo e(asset('assets/js/jquery.easing.min.js')); ?>"></script>

    <!-- Custom scripts for all pages-->
    <script src="<?php echo e(asset('assets/js/sb-admin-2.min.js')); ?>"></script>

    <!-- Additional Charges JS -->
    <script>
        $(document).ready(function() {
            // Initialize with the room subtotal value from the hidden input
            let roomSubTotal = <?php echo e($invoice->booking->total_price); ?>;

            // Function to calculate the total amount
            function calculateTotalAmount() {
                let total = roomSubTotal; // Start with the room subtotal
                $('input[name="additional_charge_amount[]"]').each(function() {
                    let amount = parseFloat($(this).val()) || 0;
                    total += amount;
                });
                $('#total_amount').val(total.toFixed(2));
            }

            // Append new charge input fields dynamically
            let baseChargeHtml = `
        <div class="form-group row additional-charge-row">
            <div class="col-md-6">
                <label for="additional_charge_description">Description</label>
                <input type="text" name="additional_charge_description[]" class="form-control" placeholder="Description of charge">
            </div>
            <div class="col-md-3">
                <label for="additional_charge_amount">Amount</label>
                <input type="number" name="additional_charge_amount[]" class="form-control text-right" placeholder="Amount">
            </div>
            <div class="col-md-3 d-flex align-items-end">
                <button type="button" class="btn btn-danger remove-charge-btn"><i class="fa fa-trash"></i></button>
            </div>
        </div>
    `;

            // Add new charge row
            $('#add-charge-btn').click(function() {
                $('#additional-charges-container').append(baseChargeHtml);
            });

            // Remove charge row and recalculate total
            $(document).on('click', '.remove-charge-btn', function() {
                $(this).closest('.additional-charge-row').remove();
                calculateTotalAmount();
            });

            // Recalculate total whenever an additional charge amount is changed
            $(document).on('input', 'input[name="additional_charge_amount[]"]', function() {
                calculateTotalAmount();
            });

            // Trigger initial calculation on page load
            calculateTotalAmount();
        });
    </script>
</body>

</html>
<?php /**PATH C:\Users\<USER>\Documents\GitHub\hotel_management_system\resources\views\admin\invoices\edit-invoices.blade.php ENDPATH**/ ?>