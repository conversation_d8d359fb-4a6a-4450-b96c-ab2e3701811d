<?php echo $__env->make('layout.admin-header', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<?php
    $currencySymbol = config('currency.symbol');
?>

<body id="page-top">

    <!-- Page Wrapper -->
    <div id="wrapper">

        <!-- Sidebar -->
        <?php echo $__env->make('layout.admin-sidebar', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        <!-- End of Sidebar -->

        <!-- Content Wrapper -->
        <div id="content-wrapper" class="d-flex flex-column">

            <!-- Main Content -->
            <div id="content">

                <!-- Topbar -->
                <?php echo $__env->make('layout.admin-topbar', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                <!-- End of Topbar -->

                <!-- Begin Page Content -->
                <div class="container-fluid">
                    <?php echo $__env->make('shared.success-message', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                    <?php echo $__env->make('shared.error-message', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">Pending Remittances</h6>
                            <div class="row">
                                <div class="form-group col-md-3">
                                    <select class="form-control" id="to_user_email" name="to_user_email">
                                        <option value="">--Select Remittance Option--</option>
                                        <?php $__currentLoopData = $users; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $user): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <option value="<?php echo e($user->email); ?>"><?php echo e($user->name); ?> -
                                                <?php echo e($user->email); ?></option>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        <option value="bank">Bank Direct</option>
                                    </select>
                                </div>

                                <div class="form-group col-md-3">
                                    <button id="remit-button" class="btn btn-primary">Remit Selected</button>
                                </div>
                            </div>
                        </div>
                        <div class="card-body">
                            
                            <div class="table-responsive">
                                <table id="myRequests" class="table table-bordered table-striped" width="100%"
                                    cellspacing="0">
                                    <thead>
                                        <tr>
                                            <th>Select</th>
                                            <th>Payment ID</th>
                                            <th>From User Email</th>
                                            <th>To User Email</th>
                                            <th>Amount</th>
                                            <th>Location</th>
                                            <th>Remittance Date</th>
                                            <th>Confirmed By</th>
                                            <th>Status</th>
                                            <th>Action</th>

                                        </tr>
                                    </thead>
                                    <!-- Moment -->
                                    <script src="<?php echo e(asset('assets/js/moment.min.js')); ?>"></script>
                                    <tbody>
                                        <?php $__currentLoopData = $pending_remittances; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $remittance): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <tr>

                                                <td><input type="checkbox" class="remittance-checkbox"
                                                        value="<?php echo e($remittance->id); ?>"></td>

                                                <td><?php echo e($remittance->payment_id); ?></td>
                                                <td><?php echo e($remittance->from_user_email); ?></td>
                                                <td><?php echo e($remittance->to_user_email); ?></td>
                                                <td class="text-right"><?php echo e($currencySymbol); ?><?php echo e(number_format($remittance->amount,2)); ?></td>
                                                <td><?php echo e($remittance->location); ?></td>
                                                <td class="date" data-date="<?php echo e($remittance->remittance_date); ?>"></td>
                                                <td><?php echo e($remittance->confirmed_by); ?></td>
                                                <td><?php echo e($remittance->status); ?></td>
                                                <td>
                                                    <?php if($remittance->status != 'confirmed'): ?>
                                                        <a href="<?php echo e(route('admin.remittances.confirm', $remittance->id)); ?>"
                                                            class="btn btn-primary btn-circle btn-sm">
                                                            <i class="fas fa-check"></i>
                                                        </a>
                                                    <?php else: ?>
                                                        <a href="<?php echo e(route('admin.remittances.bank.single', $remittance->id)); ?>"
                                                            class="btn btn-success btn-circle btn-sm">
                                                            <i class="fas fa-landmark"></i>
                                                        </a>
                                                    <?php endif; ?>
                                                </td>
                                            </tr>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                                    </tbody>

                                </table>
                                
                                <script>
                                    document.addEventListener('DOMContentLoaded', function() {
                                        $('#remit-button').on('click', function() {
                                            let selectedRemittances = [];
                                            let from_user_email = '<?php echo e(get_user_email()); ?>'; // Ensure this function outputs the correct email format
                                    
                                            // Get selected remittances
                                            $('.remittance-checkbox:checked').each(function() {
                                                selectedRemittances.push($(this).val());
                                            });
                                    
                                            // Get the email of the user to remit to from the dropdown
                                            let to_user_email = $('#to_user_email').val();
                                    
                                            if (selectedRemittances.length === 0) {
                                                alert('Please select at least one remittance to process.');
                                                return;
                                            }
                                    
                                            // Convert array to a comma-separated string
                                            let remittancesString = selectedRemittances.join(',');
                                    
                                            // Make the AJAX request
                                            $.ajax({
                                                url: '<?php echo e(route('admin.remittances.transfer')); ?>',
                                                method: 'POST',
                                                data: {
                                                    _token: '<?php echo e(csrf_token()); ?>',
                                                    remittances: selectedRemittances,
                                                    from_user_email: from_user_email, // Include the current user's email
                                                    to_user_email: to_user_email,
                                                },
                                                success: function(response) {
                                                    if (response.success) {
                                                        if (to_user_email === 'bank') {
                                                            // Redirect to the bank view
                                                            window.location.href = '<?php echo e(route("admin.remittances.bank")); ?>?remittances=' + encodeURIComponent(remittancesString) + '&from_user_email=' + encodeURIComponent(from_user_email);
                                                        } else {
                                                            alert('Successfully Remitted.');
                                                            location.reload();
                                                        }
                                                    } else {
                                                        alert('Failed to remit payments.');
                                                    }
                                                },
                                                error: function(xhr) {
                                                    let errorMessage = 'An error occurred: ' + xhr.statusText;
                                                    if (xhr.responseJSON && xhr.responseJSON.errors) {
                                                        errorMessage = 'Validation Errors: ' + Object.values(xhr.responseJSON.errors).flat().join(', ');
                                                    }
                                                    alert(errorMessage);
                                                }
                                            });
                                        });
                                    });
                                    </script>
                                    
                                    

                                <script>
                                    document.addEventListener('DOMContentLoaded', function() {
                                        // Function to format dates using moment.js
                                        function formatDate(dateString) {
                                            return moment(dateString).format('MMMM D, YYYY h:mm A');
                                        }

                                        // Format all check-in dates
                                        document.querySelectorAll('.date').forEach(function(element) {
                                            const date = element.getAttribute('data-date');
                                            element.textContent = formatDate(date);
                                        });
                                    });
                                </script>
                            </div>
                        </div>
                    </div>


                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">All Remittances</h6>
                            <div class="row">
                                
                            </div>
                        </div>
                        <div class="card-body">
                            
                            <div class="table-responsive">
                                <table id="myRequests2" class="table table-bordered table-striped" width="100%"
                                    cellspacing="0">
                                    <thead>
                                        <tr>
                                            <th>Payment ID</th>
                                            <th>From User Email</th>
                                            <th>To User Email</th>
                                            <th>Amount</th>
                                            <th>Location</th>
                                            <th>Remittance Date</th>
                                            <th>Confirmed By</th>
                                            <th>Status</th>

                                        </tr>
                                    </thead>
                                    
                                    <tbody>
                                        <?php $__currentLoopData = $all_remittances; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $remittance): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <tr>

                                                <td><?php echo e($remittance->payment_id); ?></td>
                                                <td><?php echo e($remittance->from_user_email); ?></td>
                                                <td><?php echo e($remittance->to_user_email); ?></td>
                                                <td class="text-right"><?php echo e($currencySymbol); ?><?php echo e(number_format($remittance->amount,2)); ?></td>
                                                <td><?php echo e($remittance->location); ?></td>
                                                <td class="date" data-date="<?php echo e($remittance->remittance_date); ?>"></td>
                                                <td><?php echo e($remittance->confirmed_by); ?></td>
                                                <td><?php echo e($remittance->status); ?></td>
                                                
                                            </tr>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                                    </tbody>

                                </table>
                               
                            </div>
                        </div>
                    </div>



                </div>
                <!-- /.container-fluid -->

            </div>
            <!-- End of Main Content -->

            <!-- Footer -->
            <?php echo $__env->make('layout.admin-footer', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
            <!-- End of Footer -->

        </div>
        <!-- End of Content Wrapper -->

    </div>
    <!-- End of Page Wrapper -->

    <!-- Scroll to Top Button-->
    <a class="scroll-to-top rounded" href="#page-top">
        <i class="fas fa-angle-up"></i>
    </a>

    <!-- Logout Modal-->
    <div class="modal fade" id="logoutModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel"
        aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="exampleModalLabel">Ready to Leave?</h5>
                    <button class="close" type="button" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">×</span>
                    </button>
                </div>
                <div class="modal-body">Select "Logout" below if you are ready to end your current session.</div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" type="button" data-dismiss="modal">Cancel</button>
                    <a class="btn btn-primary" href="login.html">Logout</a>
                </div>
            </div>
        </div>
    </div>

    <?php echo $__env->make('layout.scripts', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

</body>

</html>
<?php /**PATH C:\Users\<USER>\Documents\GitHub\hotel_management_system\resources\views\admin\remittances\view-remittances.blade.php ENDPATH**/ ?>