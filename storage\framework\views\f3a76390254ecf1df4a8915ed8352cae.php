<?php echo $__env->make('layout.admin-header', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<?php
    $currencySymbol = config('currency.symbol');
?>

<body id="page-top">

    <!-- Page Wrapper -->
    <div id="wrapper">

        <!-- Sidebar -->
        <?php echo $__env->make('layout.admin-sidebar', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        <!-- End of Sidebar -->

        <!-- Content Wrapper -->
        <div id="content-wrapper" class="d-flex flex-column">

            <!-- Main Content -->
            <div id="content">

                <!-- Topbar -->
                <?php echo $__env->make('layout.admin-topbar', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                <!-- End of Topbar -->

                <!-- Begin Page Content -->
                <div class="container-fluid">
                    <?php echo $__env->make('shared.success-message', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                    <?php echo $__env->make('shared.error-message', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">Booking Invoices</h6>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table id="myRequests" class="table table-bordered table-striped" width="100%"
                                    cellspacing="0">
                                    <thead>
                                        <tr>
                                            <th>Inv ID</th>
                                            <th>Book ID</th>
                                            <th>Guest Name</th>
                                            <th>Room</th>
                                            <th>Room Type</th>
                                            <th>Total Amount</th>
                                            <th>Invoice Date</th>
                                            <th>Status</th>
                                            <th>Actions</th>
                                            <th>QR Code</th> <!-- New Column for QR Codes -->
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php $__currentLoopData = $invoices; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $invoice): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <tr>
                                                <td><?php echo e($invoice->id); ?></td>
                                                <td><?php echo e($invoice->booking_id); ?></td>
                                                <td>
                                                    <?php if($invoice->booking): ?>
                                                        <?php echo e($invoice->booking->guest_first_name . ' ' . $invoice->booking->guest_last_name); ?>

                                                    <?php else: ?>
                                                        N/A
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <?php if($invoice->booking): ?>
                                                        <?php echo e($invoice->booking->room->room_number); ?>

                                                    <?php else: ?>
                                                        N/A
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <?php if($invoice->booking): ?>
                                                        <?php echo e($invoice->booking->room->roomType->name); ?>

                                                    <?php else: ?>
                                                        N/A
                                                    <?php endif; ?>

                                                </td>
                                                <td style="text-align:right;"><?php echo e($currencySymbol . number_format($invoice->total_amount,2)); ?></td>
                                                <td class="date" data-date="<?php echo e($invoice->invoice_date); ?>"></td>
                                                <td><?php echo e($invoice->status); ?></td>
                                                <!-- Moment -->
                                                <script src="<?php echo e(asset('assets/js/moment.min.js')); ?>"></script>
                                                
                                                <script>
                                                    document.addEventListener('DOMContentLoaded', function() {
                                                        // Function to format dates using moment.js
                                                        function formatDate(dateString) {
                                                            return moment(dateString).format('MMMM D, YYYY h:mm A');
                                                        }

                                                        // Format all check-in dates
                                                        document.querySelectorAll('.date').forEach(function(element) {
                                                            const date = element.getAttribute('data-date');
                                                            element.textContent = formatDate(date);
                                                        });

                                                    });
                                                </script>
                                                <td>
                                                    <a href="<?php echo e(route('admin.invoice.edit', $invoice->id)); ?>"
                                                        class="btn btn-primary btn-circle btn-sm" title="Edit">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <form action="<?php echo e(route('admin.invoice.destroy', $invoice->id)); ?>"
                                                        method="POST" style="display: inline;">
                                                        <?php echo csrf_field(); ?>
                                                        <?php echo method_field('DELETE'); ?>
                                                        <button type="submit" class="btn btn-danger btn-circle btn-sm" title="Delete">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </form>

                                                    
                                                    <a target="#" href="<?php echo e(route('admin.payments.paypal', $invoice->id)); ?>"
                                                        class="btn btn-success btn-circle btn-sm" title="Paypal">
                                                        <i class="fab fa-paypal"></i>
                                                    </a>

                                                    
                                                    <a href="<?php echo e(route('admin.payments.cash', $invoice->id)); ?>"
                                                        class="btn btn-success btn-circle btn-sm" title="Pay">
                                                        <i class="fas fa-money-bill-wave"></i>
                                                    </a>

                                                    
                                                    <a target="#" href="<?php echo e(route('admin.payments.paymongo', $invoice->id)); ?>"
                                                        class="btn btn-success btn-circle btn-sm" title="Paymongo">
                                                        <i class="fas fa-money-bill-wave"></i>
                                                    </a>
                                                </td>

                                                <!-- QR Codes -->
                                                <td>
                                                    <a target="_blank"
                                                        href="<?php echo e(route('generate.qr', ['type' => 'paypal', 'id' => $invoice->id])); ?>">
                                                        Paypal QR
                                                    </a>
                                                    <a target="_blank"
                                                        href="<?php echo e(route('generate.qr', ['type' => 'paymongo', 'id' => $invoice->id])); ?>">
                                                        Paymongo QR
                                                    </a>

                                                </td>
                                            </tr>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </tbody>
                                </table>

                                <script>
                                    document.addEventListener('DOMContentLoaded', function() {
                                        // Function to format dates using moment.js
                                        function formatDate(dateString) {
                                            return moment(dateString).format('MMMM D, YYYY h:mm A');
                                        }

                                        // Format all check-in dates
                                        document.querySelectorAll('.checkin-date').forEach(function(element) {
                                            const date = element.getAttribute('data-date');
                                            element.textContent = formatDate(date);
                                        });

                                        // Format all check-out dates
                                        document.querySelectorAll('.checkout-date').forEach(function(element) {
                                            const date = element.getAttribute('data-date');
                                            element.textContent = formatDate(date);
                                        });
                                    });
                                </script>
                            </div>
                        </div>
                    </div>
                    
                </div>
                <!-- /.container-fluid -->

            </div>
            <!-- End of Main Content -->

            <!-- Footer -->
            <?php echo $__env->make('layout.admin-footer', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
            <!-- End of Footer -->

        </div>
        <!-- End of Content Wrapper -->

    </div>
    <!-- End of Page Wrapper -->

    <!-- Scroll to Top Button-->
    <a class="scroll-to-top rounded" href="#page-top">
        <i class="fas fa-angle-up"></i>
    </a>

    <!-- Logout Modal-->
    <div class="modal fade" id="logoutModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel"
        aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="exampleModalLabel">Ready to Leave?</h5>
                    <button class="close" type="button" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">×</span>
                    </button>
                </div>
                <div class="modal-body">Select "Logout" below if you are ready to end your current session.</div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" type="button" data-dismiss="modal">Cancel</button>
                    <a class="btn btn-primary" href="login.html">Logout</a>
                </div>
            </div>
        </div>
    </div>

    <?php echo $__env->make('layout.scripts', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

</body>

</html>
<?php /**PATH C:\Users\<USER>\Documents\GitHub\hotel_management_system\resources\views\admin\invoices\view-invoices.blade.php ENDPATH**/ ?>