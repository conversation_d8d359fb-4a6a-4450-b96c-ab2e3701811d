<?php echo $__env->make('layout.admin-header', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

<?php
$currencySymbol = config('currency.symbol');
?>
<body id="page-top">

    <!-- Page Wrapper -->
    <div id="wrapper">

        <!-- Sidebar -->
        <?php echo $__env->make('layout.admin-sidebar', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        <!-- End of Sidebar -->

        <!-- Content Wrapper -->
        <div id="content-wrapper" class="d-flex flex-column">

            <!-- Main Content -->
            <div id="content">

                <!-- Topbar -->
                <?php echo $__env->make('layout.admin-topbar', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                <!-- End of Topbar -->

                <!-- Begin Page Content -->
                <div class="container-fluid">
                    <?php echo $__env->make('shared.success-message', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                    <?php echo $__env->make('shared.error-message', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">Add Room</h6>
                            <a href="<?php echo e(route('admin.rooms')); ?>" class="btn btn-danger float-right">BACK</a>
                        </div>
                        <div class="card-body">
                            
                            <form action="<?php echo e(route('admin.room.store')); ?>" method="POST">
                                <?php echo csrf_field(); ?>
                                <div class="row">
                                    <div class="form-group col-md-6">
                                        <label for="room_number">Room Name</label>
                                        <input type="text" class="form-control" id="room_number" name="room_number"
                                            placeholder="*" required></input>
                                    </div>

                                    <div class="form-group col-md-6">
                                        <label for="room_type">Room Type</label>
                                        <select class="form-control" id="room_type" name="room_type">
                                            <option value="">--Select Room Type--</option>
                                            <?php $__currentLoopData = $room_types; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $room_type): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <option value="<?php echo e($room_type->id); ?>" data-rate="<?php echo e($room_type->rate); ?>">
                                                    <?php echo e($room_type->name); ?>

                                                </option>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </select>
                                    </div>
                                    
                                    <div class="form-group col-md-6">
                                        <label for="individual_room_rate">Individual Room Rate</label>
                                        <input type="number" class="form-control" id="individual_room_rate" name="individual_room_rate"
                                            placeholder="0" required>
                                    </div>

                                    

                                    <div class="form-group col-md-6">
                                        <label for="floor">Floor</label>
                                        <input class="form-control" id="floor" name="floor"
                                            placeholder="---"></input>
                                    </div>

                                   

                                    <!-- Moment -->
                                    <script src="<?php echo e(asset('assets/js/moment.min.js')); ?>"></script>
                                   




                                    <div class="form-group col-md-12">
                                        <button type="submit" class="btn btn-primary">Add Room</button>
                                    </div>

                            </form>

                        </div>
                    </div>
                </div>
            </div>
            <!-- /.container-fluid -->

        </div>
        <!-- End of Main Content -->

        <!-- Footer -->
        <?php echo $__env->make('layout.admin-footer', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        <!-- End of Footer -->

    </div>
    <!-- End of Content Wrapper -->

    </div>
    <!-- End of Page Wrapper -->

    <!-- Scroll to Top Button-->
    <a class="scroll-to-top rounded" href="#page-top">
        <i class="fas fa-angle-up"></i>
    </a>

    <!-- Logout Modal-->
    <div class="modal fade" id="logoutModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel"
        aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="exampleModalLabel">Ready to Leave?</h5>
                    <button class="close" type="button" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">×</span>
                    </button>
                </div>
                <div class="modal-body">Select "Logout" below if you are ready to end your current session.</div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" type="button" data-dismiss="modal">Cancel</button>
                    <a class="btn btn-primary" href="login.html">Logout</a>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap core JavaScript-->
    <script src="<?php echo e(asset('assets/js/jquery.min.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/js/bootstrap.bundle.min.js')); ?>"></script>


    <!-- Core plugin JavaScript-->
    <script src="<?php echo e(asset('assets/js/jquery.easing.min.js')); ?>"></script>

    <!-- Custom scripts for all pages-->
    <script src="<?php echo e(asset('assets/js/sb-admin-2.min.js')); ?>"></script>



    <!--Datetime Picker JS and CSS-->
    <script src="<?php echo e(asset('assets/js/datetimepicker.js')); ?>"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            const roomTypeSelect = document.getElementById('room_type');
            const roomRateInput = document.getElementById('individual_room_rate');

            roomTypeSelect.addEventListener('change', function () {
                const selectedOption = this.options[this.selectedIndex];
                const roomRate = selectedOption.getAttribute('data-rate');

                // If a room type is selected, recommend the price
                if (roomRate) {
                    roomRateInput.placeholder = ' (Minumum Recommendeded Price: ' + roomRate + ')';
                    roomRateInput.value = roomRate;
                } else {
                    roomRateInput.placeholder = ''; // Clear the input if no room type is selected
                    roomRateInput.value = 0;
                }
            });
        });
    </script>

    <script>
        //vince added block
        $(document).ready(function() {
            $('input[required]').each(function() {
                // Find the label associated with the required input
                var label = $("label[for='" + $(this).attr('id') + "']");
                
                // Append a red asterisk to the label if it doesn't already have one
                if (label.length && !label.find('.required-asterisk').length) {
                    label.append('<span class="required-asterisk" style="color: red;font-size:1.2em;"> *</span>');
                }
            });
        });
        //vince end
    </script>




</body>

</html>
<?php /**PATH C:\Users\<USER>\Documents\GitHub\hotel_management_system\resources\views\admin\rooms\add-rooms.blade.php ENDPATH**/ ?>