<?php echo $__env->make('layout.admin-header', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>


<body id="page-top">

    <!-- Page Wrapper -->
    <div id="wrapper">

        <!-- Sidebar -->
        <?php echo $__env->make('layout.admin-sidebar', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        <!-- End of Sidebar -->

        <!-- Content Wrapper -->
        <div id="content-wrapper" class="d-flex flex-column">

            <!-- Main Content -->
            <div id="content">

                <!-- Topbar -->
                <?php echo $__env->make('layout.admin-topbar', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                <!-- End of Topbar -->

                <!-- Begin Page Content -->
                <div class="container-fluid">
                    <?php echo $__env->make('shared.success-message', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                    <?php echo $__env->make('shared.error-message', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">Add Item</h6>
                            <a href="<?php echo e(route('admin.ingredients')); ?>" class="btn btn-danger float-right">BACK</a>
                        </div>
                        <div class="card-body">

                            <!-- Include Select2 CSS -->
                            <link href="<?php echo e(asset('assets/css/select2.min.css')); ?>" rel="stylesheet" />
                            <!-- Include Select2 JS -->
                            <script src="<?php echo e(asset('assets/js/select2.min.js')); ?>" defer></script>
                            
                            <form action="<?php echo e(route('admin.ingredient.store')); ?>" method="POST">
                                <?php echo csrf_field(); ?>
                            <div class="row">
                                <div class="form-group col-md-6 mb-3">
                                    <label for="ingredient_name">Item Name</label>
                                    <input type="text" id="ingredient_name" name="ingredient_name" required class="form-control">
                                </div>
                                <div class="form-group col-md-6 mb-3">
                                    <label for="supplier">Supplier</label>
                                    <input type="text" id="supplier" name="supplier" class="form-control">
                                </div>
                                <div class="form-group col-md-3 mb-3">
                                    <label for="minqty">Minimum Quantity</label>
                                    <input type="number" id="minqty" name="minqty" value="0" class="form-control">
                                </div>
                                <div class="form-group col-md-3 mb-3">
                                    <label for="unit">Unit</label>
                                    <input type="text" id="unit" name="unit" required class="form-control">
                                </div>

                                <div class="form-group col-md-3 mb-3">
                                    <label for="cost">Cost</label>
                                    <input type="number" id="cost" name="cost" value="0" class="form-control">
                                </div>
                                
                                <div class="form-group col-md-3 mb-3">
                                    <label for="stock">Current Stock</label>
                                    <input type="number" id="stock" name="stock" value="0" class="form-control">
                                </div>
                                <div class="form-check col-md-3 pl-5 pt-4 mb-3">
                                    <input type="checkbox" id="for_pos" name="for_pos" value="1" class="form-check-input">
                                    <label for="for_pos" class="form-check-label">Can be sold (listed on POS)</label>
                                </div>
                                <div class="form-group col-md-3 mb-3">
                                    <label for="price">Price on POS</label>
                                    <input type="number" id="price" name="price" value="0" class="form-control">
                                </div>
                            </div>


                            <div class="form-group col-md-12 mb-3">
                                <button type="submit" name="ingredientAdd" class="btn btn-primary">Add Ingredient</button>
                            </div>
                            </form>




                        </div>
                    </div>
                </div>
            </div>


            <!-- End of Main Content -->
            <!-- Footer -->
            <?php echo $__env->make('layout.admin-footer', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
            <!-- End of Footer -->


        </div>
        <!-- End of Content Wrapper -->

    </div>
    <!-- End of Page Wrapper -->

    <!-- Scroll to Top Button-->
    <a class="scroll-to-top rounded" href="#page-top">
        <i class="fas fa-angle-up"></i>
    </a>

    <!-- Logout Modal-->
    <div class="modal fade" id="logoutModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel"
        aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="exampleModalLabel">Ready to Leave?</h5>
                    <button class="close" type="button" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">×</span>
                    </button>
                </div>
                <div class="modal-body">Select "Logout" below if you are ready to end your current session.</div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" type="button" data-dismiss="modal">Cancel</button>
                    <a class="btn btn-primary" href="login.html">Logout</a>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap core JavaScript-->
    

    <!-- Core plugin JavaScript-->
    

    <!-- Custom scripts for all pages-->
    

    <!-- Datetime Picker JS and CSS-->
    

    <?php echo $__env->make('layout.scripts', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?> 
    <!-- JavaScript Initialize for Date Picker -->
    



</body>

</html>
<?php /**PATH C:\Users\<USER>\Documents\GitHub\hotel_management_system\resources\views\admin\ingredients\ingredient-add.blade.php ENDPATH**/ ?>