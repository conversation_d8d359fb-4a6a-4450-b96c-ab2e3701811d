
<?php echo $__env->make('layout.admin-header', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

<body id="page-top">

    <!-- Page Wrapper -->
    <div id="wrapper">

        <!-- Sidebar -->
        <?php echo $__env->make('layout.admin-sidebar', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        <!-- End of Sidebar -->

        <!-- Content Wrapper -->
        <div id="content-wrapper" class="d-flex flex-column">

            <!-- Main Content -->
            <div id="content">

                <!-- Topbar -->
                <?php echo $__env->make('layout.admin-topbar', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                <!-- End of Topbar -->

                <!-- Begin Page Content -->
                <div class="container-fluid">
                    <?php echo $__env->make('shared.success-message', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                    <?php echo $__env->make('shared.error-message', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">View POS Invoice</h6>
                                <a href="<?php echo e(route('admin.posinvoices')); ?>" class="btn btn-danger float-right">BACK</a>
                        </div>
                        <div class="card-body">
                            <!-- Invoice Edit Form -->
                            <form action="<?php echo e(route('admin.posinvoice.print', $invoice->id)); ?>" method="GET" target="_blank">
                                <?php echo csrf_field(); ?>
                                <?php echo method_field('PATCH'); ?>
                                <div class="row">

                                    <!-- Booking Details -->
                                   

                                    <div class="form-group col-md-3">
                                        <label for="invoice_date">Invoice Date</label>
                                        <input type="text" id="invoice_date" name="invoice_date" class="form-control"
                                        value="<?php echo e(\Carbon\Carbon::parse($invoice->invoice_date)->format('m/d/Y')); ?>"
                                            readonly>
                                    </div>

                         

                                    <!-- Total Price -->
                                    <div class="form-group col-md-3">
                                        <label for="total_amount">Total Amount</label>
                                        <input type="text" id="total_amount" name="total_amount" class="form-control text-right" 
                                            value="<?php echo e($invoice->total_amount); ?>" readonly>
                                    </div>
                                    <div class="form-group col-md-3">
                                        <label for="total_amount">Customer Name</label>
                                        <input type="text" id="total_amount" name="total_amount" class="form-control" value="<?php echo e($order->customer_name); ?>" readonly>
                                    </div>
                                    <div class="form-group col-md-3">
                                        <label for="status">Status</label>
                                        <input type="text" id="status" name="status" class="form-control" value="<?php echo e($invoice->status); ?>" readonly>
                                    </div>

                                    <!-- Additional Charges Container -->
                                    
                                    <div class="col-md-6">
                                        <span class="badge badge-info">Items</span>
                                        <div class="receipt-view p-3 border rounded mb-2" id="receipt_view">
                                            <!-- Receipt Items will appear here -->
                                        </div>
                                        
                                    </div>
                                    <div class="col-md-6">
                                        <div class="row">
                                        
                                    
                                    
                                        <div class="form-group col-md-6">
                                            <label for="createdby">Created by</label>
                                            <input type="text" id="createdby" name="createdby" class="form-control" value="<?php echo e($invoice->created_by); ?>" readonly>
                                        </div>
                                    
                                    
                                        <div class="form-group col-md-6">
                                            <label for="updated_by">Updated by</label>
                                            <input type="text" id="updated_by" name="updated_by" class="form-control" value="<?php echo e($invoice->updated_by); ?>" readonly>
                                        </div>
                                        </div>
                                    </div>

                                <div class="form-group col-md-12 text-center">
                                    <button type="submit" class="btn btn-primary">Print to POS Printer</button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
                <!-- /.container-fluid -->

            </div>
            <!-- End of Main Content -->

            <!-- Footer -->
            <?php echo $__env->make('layout.admin-footer', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
            <!-- End of Footer -->

        </div>
        <!-- End of Content Wrapper -->

    </div>
    <!-- End of Page Wrapper -->

    <!-- Scroll to Top Button-->
    <a class="scroll-to-top rounded" href="#page-top">
        <i class="fas fa-angle-up"></i>
    </a>

    <!-- Logout Modal-->
    <div class="modal fade" id="logoutModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel"
        aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="exampleModalLabel">Ready to Leave?</h5>
                    <button class="close" type="button" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">×</span>
                    </button>
                </div>
                <div class="modal-body">Select "Logout" below if you are ready to end your current session.</div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" type="button" data-dismiss="modal">Cancel</button>
                    <a class="btn btn-primary" href="login.html">Logout</a>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap core JavaScript-->
    <script src="<?php echo e(asset('assets/js/jquery.min.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/js/bootstrap.bundle.min.js')); ?>"></script>

    <!-- Core plugin JavaScript-->
    <script src="<?php echo e(asset('assets/js/jquery.easing.min.js')); ?>"></script>

    <!-- Custom scripts for all pages-->
    <script src="<?php echo e(asset('assets/js/sb-admin-2.min.js')); ?>"></script>

    <?php
        $orderedItems = [];
        foreach ($order->orderItems as $item) {
            $orderedItems[] = [
                'id' => $item->menu_id,
                'name' => $item->menu->name,
                'price' => $item->price,
                'quantity' => $item->quantity
            ];
        }
    ?>
    <!-- Additional Charges JS -->
    <script>
        function updateReceiptView(orderItems) {
            const receiptView = document.getElementById('receipt_view');
            receiptView.innerHTML = '';

            itemtotal = 0;
            orderItems.forEach((item, index) => {
                itemtotal = item.price * item.quantity;
                const itemRow = document.createElement('div');
                itemRow.className = 'row';

                itemRow.innerHTML = `
                    <div class="col-md-8" style="margin-bottom:2px;">${item.name} x${item.quantity}</div>
                    <div class="col-md-4" style="margin-bottom:2px;text-align:right;">${itemtotal.toFixed(2)}</div>
                    
                `;//vince added div classes and styles

                receiptView.appendChild(itemRow);
            });
        };
        $(document).ready(function() {
            let orderItems = [];
            orderItems = <?php echo json_encode($orderedItems, 15, 512) ?>;
            updateReceiptView(orderItems);
        });
    </script>
</body>

</html>
<?php /**PATH C:\Users\<USER>\Documents\GitHub\hotel_management_system\resources\views\admin\invoices\view-posinv.blade.php ENDPATH**/ ?>