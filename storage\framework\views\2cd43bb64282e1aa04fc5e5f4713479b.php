<?php echo $__env->make('layout.admin-header', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

<body id="page-top">
    <!-- Page Wrapper -->
    <div id="wrapper" data-bs-theme="light">
        <!-- Sidebar -->
        <?php echo $__env->make('layout.admin-sidebar', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        <!-- End of Sidebar -->

        <!-- Content Wrapper -->
        <div id="content-wrapper" class="d-flex flex-column">
            <!-- Main Content -->
            <div id="content">
                <!-- Topbar -->
                <?php echo $__env->make('layout.admin-topbar', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                <!-- End of Topbar -->

                <!-- Begin Page Content -->
                <div class="container-fluid">
                    <?php echo $__env->make('shared.success-message', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                    <?php echo $__env->make('shared.error-message', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

                    <!-- Page Heading -->
                    <div class="d-sm-flex align-items-center justify-content-between mb-4">
                        <h1 class="h3 mb-0 text-gray-800">Hotel Management</h1>
                        <a href="<?php echo e(route('admin.hotels.create')); ?>" class="d-none d-sm-inline-block btn btn-sm btn-primary shadow-sm">
                            <i class="fas fa-plus fa-sm text-white-50"></i> Add New Hotel
                        </a>
                    </div>

                    <!-- Hotels Table -->
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">All Hotels</h6>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-bordered" id="dataTable" width="100%" cellspacing="0">
                                    <thead>
                                        <tr>
                                            <th>ID</th>
                                            <th>Name</th>
                                            <th>Email</th>
                                            <th>Phone</th>
                                            <th>Status</th>
                                            <th>Users</th>
                                            <th>Created</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php $__currentLoopData = $hotels; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $hotel): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <tr>
                                            <td><?php echo e($hotel->id); ?></td>
                                            <td>
                                                <strong><?php echo e($hotel->name); ?></strong>
                                                <br>
                                                <small class="text-muted"><?php echo e($hotel->slug); ?></small>
                                            </td>
                                            <td><?php echo e($hotel->email); ?></td>
                                            <td><?php echo e($hotel->phone ?? 'N/A'); ?></td>
                                            <td>
                                                <span class="badge badge-<?php echo e($hotel->status === 'active' ? 'success' : ($hotel->status === 'suspended' ? 'danger' : 'warning')); ?>">
                                                    <?php echo e(ucfirst($hotel->status)); ?>

                                                </span>
                                            </td>
                                            <td><?php echo e($hotel->users->count()); ?></td>
                                            <td><?php echo e($hotel->created_at->format('M d, Y')); ?></td>
                                            <td>
                                                <a href="<?php echo e(route('admin.hotels.show', $hotel)); ?>" class="btn btn-info btn-sm">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="<?php echo e(route('admin.hotels.edit', $hotel)); ?>" class="btn btn-warning btn-sm">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <form action="<?php echo e(route('admin.switch-hotel')); ?>" method="POST" style="display: inline;">
                                                    <?php echo csrf_field(); ?>
                                                    <input type="hidden" name="hotel_id" value="<?php echo e($hotel->id); ?>">
                                                    <button type="submit" class="btn btn-primary btn-sm" title="Switch to this hotel">
                                                        <i class="fas fa-exchange-alt"></i>
                                                    </button>
                                                </form>
                                            </td>
                                        </tr>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </tbody>
                                </table>
                            </div>
                            
                            <!-- Pagination -->
                            <div class="d-flex justify-content-center">
                                <?php echo e($hotels->links()); ?>

                            </div>
                        </div>
                    </div>
                </div>
                <!-- /.container-fluid -->
            </div>
            <!-- End of Main Content -->

            <!-- Footer -->
            <?php echo $__env->make('layout.admin-footer', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
            <!-- End of Footer -->
        </div>
        <!-- End of Content Wrapper -->
    </div>
    <!-- End of Page Wrapper -->

    <!-- Scroll to Top Button-->
    <a class="scroll-to-top rounded" href="#page-top">
        <i class="fas fa-angle-up"></i>
    </a>
</body>
<?php /**PATH C:\Users\<USER>\Documents\GitHub\hotel_management_system\resources\views\admin\hotels\index.blade.php ENDPATH**/ ?>