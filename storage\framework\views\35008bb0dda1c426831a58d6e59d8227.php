<?php echo $__env->make('layout.admin-header', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<?php
    $currencySymbol = config('currency.symbol');
?>

<body id="page-top">

    <!-- Page Wrapper -->
    <div id="wrapper">

        <!-- Sidebar -->
        <?php echo $__env->make('layout.admin-sidebar', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        <!-- End of Sidebar -->

        <!-- Content Wrapper -->
        <div id="content-wrapper" class="d-flex flex-column">

            <!-- Main Content -->
            <div id="content">

                <!-- Topbar -->
                <?php echo $__env->make('layout.admin-topbar', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                <!-- End of Topbar -->

                <!-- Begin Page Content -->
                <div class="container-fluid px-4">
                    <h4 class="mt-4">Kitchen View</h4>
                    <?php echo $__env->make('shared.error-message', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                    <?php echo $__env->make('shared.success-message', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                    
                    <div class="row">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-header">
                                    <h6>Select Date Range</h6>
                                </div>
                                <div class="card-body">
                                    <form action="<?php echo e(route('kitchen.search')); ?>" method="GET">
                                        <div class="row">
                                            <input type="hidden" name="start_date" id="start_date">
                                            <input type="hidden" name="end_date" id="end_date">
                                            <div class="col-md-5">
                                                <label for="start_date">Start Date</label>
                                                <input type="text" class="form-control datepicker1" required autocomplete="off">
                                            </div>
                                            <div class="col-md-5">
                                                <label for="end_date">End Date</label>
                                                <input type="text" class="form-control datepicker2" required autocomplete="off">
                                            </div>
                                            
                                                <button type="submit" class="btn btn-primary mt-3">Search</button>
                                            
                                        </div>
                                        
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
            
                    
                    <?php if(isset($eventsWithMenus) && is_array($eventsWithMenus) && count($eventsWithMenus) > 0): ?> <!--vince orig ordersWithMenus-->
                        <div class="row mt-4">
                            <div class="col-md-12">
                                <h4>Kitchen Schedule For
                                    
                                    <?php echo e(\Carbon\Carbon::parse(Request::get('start_date'))->format('F j, Y')); ?> - 
                                    <?php echo e(\Carbon\Carbon::parse(Request::get('end_date'))->format('F j, Y')); ?>

                                </h4>
                                <table id="myRequests" class="table table-bordered table-striped">
                                    <thead>
                                        <tr>
                                            <th>Customer Name</th>
                                            <th>Event Name</th>
                                            <th>Event Room</th>
                                            <th>Menu Item * Qty.</th>
                                            <th>Order Date</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php $__currentLoopData = $eventsWithMenus; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $orderWithMenu): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <tr>
                                                <td><?php echo e($orderWithMenu['customer_name']); ?></td>
                                                <td><?php echo e($orderWithMenu['event_name']); ?></td>
                                                <td><?php echo e($orderWithMenu['event_room']); ?></td>
                                                <td><?php echo e($orderWithMenu['menu_name'] .' X '. $orderWithMenu['quantity']); ?></td>
                                                
                                                <td><?php echo e(\Carbon\Carbon::parse($orderWithMenu['start_date'])->format('F j, Y h:i A')); ?></td>
                                            </tr>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
                <!-- /.container-fluid -->

            </div>
            <!-- End of Main Content -->

            <!-- Footer -->
            <?php echo $__env->make('layout.admin-footer', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
            <!-- End of Footer -->

        </div>
        <!-- End of Content Wrapper -->

    </div>
    <!-- End of Page Wrapper -->

    <!-- Scroll to Top Button-->
    <a class="scroll-to-top rounded" href="#page-top">
        <i class="fas fa-angle-up"></i>
    </a>

    <!-- Logout Modal-->
    <div class="modal fade" id="logoutModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel"
        aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="exampleModalLabel">Ready to Leave?</h5>
                    <button class="close" type="button" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">×</span>
                    </button>
                </div>
                <div class="modal-body">Select "Logout" below if you are ready to end your current session.</div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" type="button" data-dismiss="modal">Cancel</button>
                    <a class="btn btn-primary" href="login.html">Logout</a>
                </div>
            </div>
        </div>
    </div>

    <?php echo $__env->make('layout.scripts', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

</body>

<script>
    $(document).ready(function() {
        $('.datepicker1').datepicker({
            showOnFocus: true,
            dateFormat: 'mm/dd/yy', // Displayed date format
            altFormat: 'yy-mm-dd', // Format for form submission
            altField: "#start_date" // IDs of hidden input fields to store formatted dates
        });
        <?php if (!isset($eventsWithMenus)): /*vince added block*/ ?>
            $('.datepicker1').datepicker("setDate", new Date());
        <?php endif; ?>
        $('.datepicker2').datepicker({
            showOnFocus: true,
            dateFormat: 'mm/dd/yy', // Displayed date format
            altFormat: 'yy-mm-dd', // Format for form submission
            altField: "#end_date" // IDs of hidden input fields to store formatted dates
        });
        <?php if (!isset($eventsWithMenus)): /*vince added block*/ ?>
            $('.datepicker2').datepicker("setDate", new Date());
        <?php endif; ?>  
    });
</script>


</html>
<?php /**PATH C:\Users\<USER>\Documents\GitHub\hotel_management_system\resources\views\admin\kitchen\kitchen-view.blade.php ENDPATH**/ ?>