<?php echo $__env->make('layout.admin-header', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>


<body id="page-top">


    <!-- Page Wrapper -->
    <div id="wrapper" data-bs-theme="light">

        <!-- Sidebar -->
        <?php echo $__env->make('layout.admin-sidebar', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        <!-- End of Sidebar -->

        <!-- Content Wrapper -->
        <div id="content-wrapper" class="d-flex flex-column">

            <!-- Main Content -->
            <div id="content">

                <!-- Topbar -->
                <?php echo $__env->make('layout.admin-topbar', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                <!-- End of Topbar -->

                <!-- Begin Page Content -->
                <div class="container-fluid">
                    <?php echo $__env->make('shared.success-message', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                    <?php echo $__env->make('shared.error-message', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>



                    <!-- Content Row -->
                    <div class="row">
                        <div class="container-fluid">
                            <h1 class="h3 mb-4 text-gray-800">General Settings</h1>
                            <!-- Form for general settings -->
                            <form action="<?php echo e(route('admin.settings.update')); ?>" method="POST">
                                <?php echo csrf_field(); ?>

                                <div class="row">

                                    <div class="form-group col-md-3">
                                        <label for="site_name">Business Name</label>
                                        <input autocomplete="off" type="text" name="site_name" id="site_name"
                                            class="form-control"
                                            value="<?php echo e(old('site_name', get_setting('site_name'))); ?>">
                                    </div>
                                    <div class="form-group col-md-3">
                                        <label for="contact_email">Contact Email</label>
                                        <input autocomplete="off" type="email" name="contact_email" id="contact_email"
                                            class="form-control"
                                            value="<?php echo e(old('contact_email', get_setting('contact_email'))); ?>">
                                    </div>
                                    <div class="form-group col-md-6">
                                        <label for="site_description">Site Description</label>
                                        <input autocomplete="off" type="text" name="site_description"
                                            id="site_description" class="form-control"
                                            value="<?php echo e(old('site_description', get_setting('site_description'))); ?>">
                                    </div>
                                    <div class="form-group col-md-3">
                                        <label for="phone">Phone</label>
                                        <input autocomplete="off" type="text" name="phone" id="phone"
                                            class="form-control"
                                            value="<?php echo e(old('phone', get_setting('phone'))); ?>">
                                    </div>
                                    <div class="form-group col-md-3">
                                        <label for="tin">TIN</label>
                                        <input autocomplete="off" type="text" name="tin" id="tin"
                                            class="form-control"
                                            value="<?php echo e(old('tin', get_setting('tin'))); ?>">
                                    </div>
                                    <div class="form-group col-md-6">
                                        <label for="address">Address</label>
                                        <input autocomplete="off" type="text" name="address"
                                            id="address" class="form-control"
                                            value="<?php echo e(old('address', get_setting('address'))); ?>">
                                    </div>
                                   

                                </div>

                                <button type="submit" class="btn btn-primary">Save Settings</button>
                            </form>

                        </div>
                    </div>


                </div>
                <!-- /.container-fluid -->

            </div>
            <!-- End of Main Content -->

            <!-- Footer -->
            <?php echo $__env->make('layout.admin-footer', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
            <!-- End of Footer -->

        </div>
        <!-- End of Content Wrapper -->

    </div>
    <!-- End of Page Wrapper -->

    <!-- Scroll to Top Button-->
    <a class="scroll-to-top rounded" href="#page-top">
        <i class="fas fa-angle-up"></i>
    </a>

    <!-- Logout Modal-->
    <div class="modal fade" id="logoutModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel"
        aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="exampleModalLabel">Ready to Leave?</h5>
                    <button class="close" type="button" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">×</span>
                    </button>
                </div>
                <div class="modal-body">Select "Logout" below if you are ready to end your current session.</div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" type="button" data-dismiss="modal">Cancel</button>
                    <a class="btn btn-primary" href="login.html">Logout</a>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap core JavaScript-->
    <script src="<?php echo e(asset('assets/js/jquery.min.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/js/bootstrap.bundle.min.js')); ?>"></script>

    <!-- Core plugin JavaScript-->
    <script src="<?php echo e(asset('assets/js/jquery.easing.min.js')); ?>"></script>

    <!-- Custom scripts for all pages-->
    <script src="<?php echo e(asset('assets/js/sb-admin-2.min.js')); ?>"></script>

    <!-- Page level plugins -->
    <script src="<?php echo e(asset('assets/js/Chart.min.js')); ?>"></script>

    <!-- Page level custom scripts -->
    <script src="<?php echo e(asset('assets/js/chart-area-demo.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/js/chart-pie-demo.js')); ?>"></script>

</body>

</html>
<?php /**PATH C:\Users\<USER>\Documents\GitHub\hotel_management_system\resources\views\admin\settings\general.blade.php ENDPATH**/ ?>