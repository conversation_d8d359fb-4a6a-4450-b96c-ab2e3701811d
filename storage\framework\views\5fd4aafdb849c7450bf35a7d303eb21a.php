<?php echo $__env->make('layout.admin-header', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

<body id="page-top">

    <!-- Page Wrapper -->
    <div id="wrapper">

        <!-- Sidebar -->
        <?php echo $__env->make('layout.admin-sidebar', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        <!-- End of Sidebar -->

        <!-- Content Wrapper -->
        <div id="content-wrapper" class="d-flex flex-column">

            <!-- Main Content -->
            <div id="content">

                <!-- Topbar -->
                <?php echo $__env->make('layout.admin-topbar', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                <!-- End of Topbar -->

                <!-- Begin Page Content -->
                <div class="container-fluid">
                    <?php echo $__env->make('shared.success-message', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                    <?php echo $__env->make('shared.error-message', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">Cash Checkout</h6>
                            <a href="<?php echo e(route('admin.invoices')); ?>" class="btn btn-danger float-end">BACK</a>
                        </div>
                        <div class="card-body">
                            <!-- Cash Checkout Form -->
                            <form action="<?php echo e(route('admin.payment.store.cash')); ?>" method="POST">
                                <?php echo csrf_field(); ?>
                                <div class="row">

                                    <!-- Invoice Details -->
                                    <div class="col-md-12 mb-4">
                                        <div class="card border-light">
                                            <div class="card-header bg-light">
                                                <h6 class="m-0 font-weight-bold text-secondary">Invoice Details</h6>
                                            </div>
                                            <input type="hidden" name="invoice_id" value="<?php echo e($invoice->id); ?>">
                                            <div class="card-body">
                                                <div class="row">
                                                    <div class="form-group col-md-4">
                                                        <label for="booking_id">Booking ID</label>
                                                        <input type="text" id="booking_id" name="booking_id"
                                                            class="form-control" value="<?php echo e($invoice->booking_id); ?>"
                                                            readonly>
                                                    </div>

                                                    <div class="form-group col-md-4">
                                                        <label for="status">Status</label>
                                                        <input type="text" id="status" name="status"
                                                            class="form-control" value="<?php echo e($invoice->status); ?>"
                                                            readonly>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Booking Details -->
                                    <div class="col-md-12 mb-4">
                                        <div class="card border-light">
                                            <div class="card-header bg-light">
                                                <h6 class="m-0 font-weight-bold text-secondary">Booking Details</h6>
                                            </div>
                                            <div class="card-body">
                                                <div class="row">
                                                    <div class="form-group col-md-4">
                                                        <label for="guest_name">Guest Name</label>
                                                        <input type="text" id="guest_name" class="form-control"
                                                            value="<?php echo e($invoice->booking->guest_first_name); ?> <?php echo e($invoice->booking->guest_last_name); ?>"
                                                            readonly>
                                                    </div>

                                                    <div class="form-group col-md-4">
                                                        <label for="guest_email">Guest Email</label>
                                                        <input type="text" id="guest_email" class="form-control"
                                                            value="<?php echo e($invoice->booking->guest_email); ?>" readonly>
                                                    </div>

                                                    <div class="form-group col-md-4">
                                                        <label for="checkin_date">Check-in Date</label>
                                                        <input type="text" id="checkin_date" class="form-control"
                                                            value="<?php echo e($invoice->booking->checkin_date); ?>" readonly>
                                                    </div>

                                                    <div class="form-group col-md-4">
                                                        <label for="checkout_date">Check-out Date</label>
                                                        <input type="text" id="checkout_date" class="form-control"
                                                            value="<?php echo e($invoice->booking->checkout_date); ?>" readonly>
                                                    </div>

                                                    <div class="form-group col-md-4">
                                                        <label for="room_number">Room Number</label>
                                                        <input type="text" id="room_number" class="form-control"
                                                            value="<?php echo e($invoice->booking->room->room_number); ?>"
                                                            readonly>
                                                    </div>

                                                    <div class="form-group col-md-4">
                                                        <label for="room_type">Room Type</label>
                                                        <input type="text" id="room_type" class="form-control"
                                                            value="<?php echo e($invoice->booking->room->roomType->name); ?>"
                                                            readonly>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Charges Summary -->
                                    <div class="col-md-12 mb-4">
                                        <div class="card border-light">
                                            <div class="card-header bg-light">
                                                <h6 class="m-0 font-weight-bold text-secondary">Charges Summary</h6>
                                            </div>
                                            <div class="card-body">
                                                <div class="row">
                                                    <div class="form-group col-md-6">
                                                        <label for="total_price">Base Charges</label>
                                                        <input type="text" id="total_price" class="form-control"
                                                            value="<?php echo e($invoice->booking->total_price); ?>" readonly>
                                                    </div>

                                                    <div class="form-group col-md-6">
                                                        <label for="additional_charges">Additional Charges</label>
                                                        <ul class="list-group mb-3">
                                                            <?php if($invoice->additionalCharges->isEmpty()): ?>
                                                                <li class="list-group-item">No additional charges.</li>
                                                            <?php else: ?>
                                                                <?php $__currentLoopData = $invoice->additionalCharges; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $charge): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                                    <li
                                                                        class="list-group-item d-flex justify-content-between align-items-center">
                                                                        <?php echo e($charge->description); ?>

                                                                        <span
                                                                            class="badge badge-primary badge-pill"><?php echo e($charge->amount); ?></span>
                                                                    </li>
                                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                            <?php endif; ?>
                                                        </ul>
                                                    </div>

                                                    <div class="form-group col-md-12">
                                                        <label for="amount">Total Amount Due</label>
                                                        <input type="text" id="total_amount" name="amount"
                                                            class="form-control" value="<?php echo e($invoice->total_amount); ?>"
                                                            readonly>
                                                    </div>

                                                    
                                                    <input type="hidden" name="created_by"
                                                        value="<?php echo e(get_user_email()); ?>">
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Payment Details -->
                                    <div class="col-md-12 mb-4">
                                        <div class="card border-light">
                                            <div class="card-header bg-light">
                                                <h6 class="m-0 font-weight-bold text-secondary">Payment Details</h6>
                                            </div>
                                            <div class="card-body">
                                                <div class="row">
                                                    <div class="form-group col-md-6">
                                                        <label for="amount_received">Amount Received</label>
                                                        <input type="number" id="amount_received"
                                                            name="amount_received" class="form-control"
                                                            placeholder="Enter amount received" required>
                                                    </div>

                                                    <div class="form-group col-md-6">
                                                        <label for="change">Change</label>
                                                        <input type="text" id="change" class="form-control"
                                                            readonly>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-md-12">
                                        <button type="submit" id="add_payment_button"
                                            class="btn btn-primary btn-block">Add Payment</button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
                <!-- /.container-fluid -->

            </div>
            <!-- End of Main Content -->

            <!-- Footer -->
            <?php echo $__env->make('layout.admin-footer', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
            <!-- End of Footer -->

        </div>
        <!-- End of Content Wrapper -->

    </div>
    <!-- End of Page Wrapper -->

    <!-- Scroll to Top Button-->
    <a class="scroll-to-top rounded" href="#page-top">
        <i class="fas fa-angle-up"></i>
    </a>

    <!-- Logout Modal-->
    <div class="modal fade" id="logoutModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel"
        aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="exampleModalLabel">Ready to Leave?</h5>
                    <button class="close" type="button" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">×</span>
                    </button>
                </div>
                <div class="modal-body">Select "Logout" below if you are ready to end your current session.</div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" type="button" data-dismiss="modal">Cancel</button>
                    <a class="btn btn-primary" href="login.html">Logout</a>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap core JavaScript-->
    <script src="<?php echo e(asset('assets/js/jquery.min.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/js/bootstrap.bundle.min.js')); ?>"></script>

    <!-- Core plugin JavaScript-->
    <script src="<?php echo e(asset('assets/js/jquery.easing.min.js')); ?>"></script>

    <!-- Custom scripts for all pages-->
    <script src="<?php echo e(asset('assets/js/sb-admin-2.min.js')); ?>"></script>

    <!-- Script to calculate change -->
    <script>
        document.getElementById('amount_received').addEventListener('input', function() {
            var totalAmount = parseFloat(document.getElementById('total_amount').value) || 0;
            var amountReceived = parseFloat(this.value) || 0;
            var change = amountReceived - totalAmount;

            //if change is negative, display insufficient amount
            document.getElementById('change').value = change >= 0 ? change.toFixed(2) : 'Insufficient amount';
            // Enable or disable the button based on the amount received
            document.getElementById('add_payment_button').disabled = amountReceived < totalAmount;
        });
    </script>

</body>

</html>
<?php /**PATH C:\Users\<USER>\Documents\GitHub\hotel_management_system\resources\views\admin\payments\cash-checkout.blade.php ENDPATH**/ ?>