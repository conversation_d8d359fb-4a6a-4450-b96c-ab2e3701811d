<?php echo $__env->make('layout.admin-header', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

<body id="page-top">
    <!-- Page Wrapper -->
    <div id="wrapper" data-bs-theme="light">
        <!-- Sidebar -->
        <?php echo $__env->make('layout.admin-sidebar', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        <!-- End of Sidebar -->

        <!-- Content Wrapper -->
        <div id="content-wrapper" class="d-flex flex-column">
            <!-- Main Content -->
            <div id="content">
                <!-- Topbar -->
                <?php echo $__env->make('layout.admin-topbar', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                <!-- End of Topbar -->

                <!-- Begin Page Content -->
                <div class="container-fluid">
                    <?php echo $__env->make('shared.success-message', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                    <?php echo $__env->make('shared.error-message', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

                    <!-- Page Heading -->
                    <div class="d-sm-flex align-items-center justify-content-between mb-4">
                        <h1 class="h3 mb-0 text-gray-800">Subscription Plans</h1>
                        <a href="<?php echo e(route('admin.subscription.current')); ?>" class="d-none d-sm-inline-block btn btn-sm btn-secondary shadow-sm">
                            <i class="fas fa-arrow-left fa-sm text-white-50"></i> Back to Current Plan
                        </a>
                    </div>

                    <!-- Current Plan Info -->
                    <?php if($currentHotel && $currentHotel->currentSubscription): ?>
                    <div class="alert alert-info">
                        <h6><i class="fas fa-info-circle"></i> Current Plan</h6>
                        <p class="mb-0">
                            You are currently on the <strong><?php echo e($currentHotel->currentSubscription->plan->name); ?></strong> plan 
                            (<?php echo e(ucfirst($currentHotel->currentSubscription->billing_cycle)); ?>)
                            <?php if($currentHotel->currentSubscription->onTrial()): ?>
                                - <span class="badge badge-warning">Trial (<?php echo e($currentHotel->currentSubscription->daysLeftOnTrial()); ?> days left)</span>
                            <?php endif; ?>
                        </p>
                    </div>
                    <?php endif; ?>

                    <!-- Pricing Plans -->
                    <div class="row">
                        <?php $__currentLoopData = $plans; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $plan): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="col-lg-4 col-md-6 mb-4">
                            <div class="card h-100 <?php echo e($plan->is_popular ? 'border-primary' : ''); ?>">
                                <?php if($plan->is_popular): ?>
                                <div class="card-header bg-primary text-white text-center">
                                    <i class="fas fa-star"></i> Most Popular
                                </div>
                                <?php endif; ?>
                                
                                <div class="card-body text-center">
                                    <h4 class="card-title"><?php echo e($plan->name); ?></h4>
                                    <p class="text-muted"><?php echo e($plan->description); ?></p>
                                    
                                    <div class="pricing mb-3">
                                        <h2 class="text-primary"><?php echo e($plan->formatted_price); ?></h2>
                                        <small class="text-muted">per <?php echo e($plan->billing_cycle); ?></small>
                                    </div>

                                    <ul class="list-unstyled">
                                        <?php $__currentLoopData = $plan->features; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $feature): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <li class="mb-2">
                                            <i class="fas fa-check text-success"></i> <?php echo e($feature); ?>

                                        </li>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </ul>
                                </div>
                                
                                <div class="card-footer">
                                    <?php if($currentHotel && $currentHotel->currentSubscription && $currentHotel->currentSubscription->plan->id === $plan->id): ?>
                                        <button class="btn btn-secondary btn-block" disabled>
                                            <i class="fas fa-check"></i> Current Plan
                                        </button>
                                    <?php else: ?>
                                        <?php if($plan->canAccommodateHotel($currentHotel)): ?>
                                            <a href="<?php echo e(route('admin.subscription.upgrade', $plan)); ?>" class="btn btn-primary btn-block">
                                                <i class="fas fa-arrow-up"></i> 
                                                <?php if($currentHotel && $currentHotel->currentSubscription): ?>
                                                    Upgrade to <?php echo e($plan->name); ?>

                                                <?php else: ?>
                                                    Choose <?php echo e($plan->name); ?>

                                                <?php endif; ?>
                                            </a>
                                        <?php else: ?>
                                            <button class="btn btn-warning btn-block" disabled title="This plan cannot accommodate your current setup">
                                                <i class="fas fa-exclamation-triangle"></i> Not Compatible
                                            </button>
                                            <small class="text-muted d-block mt-2">
                                                Your hotel has <?php echo e($currentHotel->rooms()->count()); ?> rooms and <?php echo e($currentHotel->users()->count()); ?> users.
                                                This plan supports up to <?php echo e($plan->max_rooms ?: 'unlimited'); ?> rooms and <?php echo e($plan->max_users ?: 'unlimited'); ?> users.
                                            </small>
                                        <?php endif; ?>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>

                    <!-- Features Comparison -->
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">Feature Comparison</h6>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-bordered">
                                    <thead>
                                        <tr>
                                            <th>Feature</th>
                                            <?php $__currentLoopData = $plans; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $plan): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <th class="text-center"><?php echo e($plan->name); ?></th>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td><strong>Rooms</strong></td>
                                            <?php $__currentLoopData = $plans; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $plan): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <td class="text-center"><?php echo e($plan->max_rooms ?: 'Unlimited'); ?></td>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </tr>
                                        <tr>
                                            <td><strong>Users</strong></td>
                                            <?php $__currentLoopData = $plans; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $plan): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <td class="text-center"><?php echo e($plan->max_users ?: 'Unlimited'); ?></td>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </tr>
                                        <tr>
                                            <td><strong>Online Booking</strong></td>
                                            <?php $__currentLoopData = $plans; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $plan): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <td class="text-center">
                                                <?php if($plan->hasFeature('Online booking')): ?>
                                                    <i class="fas fa-check text-success"></i>
                                                <?php else: ?>
                                                    <i class="fas fa-times text-danger"></i>
                                                <?php endif; ?>
                                            </td>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </tr>
                                        <tr>
                                            <td><strong>Restaurant Management</strong></td>
                                            <?php $__currentLoopData = $plans; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $plan): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <td class="text-center">
                                                <?php if($plan->hasFeature('Restaurant management')): ?>
                                                    <i class="fas fa-check text-success"></i>
                                                <?php else: ?>
                                                    <i class="fas fa-times text-danger"></i>
                                                <?php endif; ?>
                                            </td>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </tr>
                                        <tr>
                                            <td><strong>Custom Branding</strong></td>
                                            <?php $__currentLoopData = $plans; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $plan): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <td class="text-center">
                                                <?php if($plan->hasFeature('Custom branding')): ?>
                                                    <i class="fas fa-check text-success"></i>
                                                <?php else: ?>
                                                    <i class="fas fa-times text-danger"></i>
                                                <?php endif; ?>
                                            </td>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </tr>
                                        <tr>
                                            <td><strong>API Access</strong></td>
                                            <?php $__currentLoopData = $plans; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $plan): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <td class="text-center">
                                                <?php if($plan->hasFeature('API access')): ?>
                                                    <i class="fas fa-check text-success"></i>
                                                <?php else: ?>
                                                    <i class="fas fa-times text-danger"></i>
                                                <?php endif; ?>
                                            </td>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </tr>
                                        <tr>
                                            <td><strong>Support</strong></td>
                                            <?php $__currentLoopData = $plans; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $plan): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <td class="text-center">
                                                <?php if($plan->hasFeature('24/7 phone support')): ?>
                                                    24/7 Phone
                                                <?php elseif($plan->hasFeature('Priority support')): ?>
                                                    Priority Email
                                                <?php else: ?>
                                                    Email
                                                <?php endif; ?>
                                            </td>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- FAQ Section -->
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">Frequently Asked Questions</h6>
                        </div>
                        <div class="card-body">
                            <div class="accordion" id="faqAccordion">
                                <div class="card">
                                    <div class="card-header" id="faq1">
                                        <h2 class="mb-0">
                                            <button class="btn btn-link" type="button" data-toggle="collapse" data-target="#collapse1">
                                                Can I change my plan anytime?
                                            </button>
                                        </h2>
                                    </div>
                                    <div id="collapse1" class="collapse" data-parent="#faqAccordion">
                                        <div class="card-body">
                                            Yes, you can upgrade or downgrade your plan at any time. Changes take effect immediately, and billing is prorated.
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="card">
                                    <div class="card-header" id="faq2">
                                        <h2 class="mb-0">
                                            <button class="btn btn-link" type="button" data-toggle="collapse" data-target="#collapse2">
                                                What happens if I exceed my plan limits?
                                            </button>
                                        </h2>
                                    </div>
                                    <div id="collapse2" class="collapse" data-parent="#faqAccordion">
                                        <div class="card-body">
                                            If you exceed your plan limits, you'll be prompted to upgrade to a higher plan. We'll help you migrate seamlessly.
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="card">
                                    <div class="card-header" id="faq3">
                                        <h2 class="mb-0">
                                            <button class="btn btn-link" type="button" data-toggle="collapse" data-target="#collapse3">
                                                Is there a setup fee?
                                            </button>
                                        </h2>
                                    </div>
                                    <div id="collapse3" class="collapse" data-parent="#faqAccordion">
                                        <div class="card-body">
                                            No, there are no setup fees. You only pay the monthly or yearly subscription fee for your chosen plan.
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- /.container-fluid -->
            </div>
            <!-- End of Main Content -->

            <!-- Footer -->
            <?php echo $__env->make('layout.admin-footer', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
            <!-- End of Footer -->
        </div>
        <!-- End of Content Wrapper -->
    </div>
    <!-- End of Page Wrapper -->

    <!-- Scroll to Top Button-->
    <a class="scroll-to-top rounded" href="#page-top">
        <i class="fas fa-angle-up"></i>
    </a>
</body>
<?php /**PATH C:\Users\<USER>\Documents\GitHub\hotel_management_system\resources\views\admin\subscriptions\plans.blade.php ENDPATH**/ ?>