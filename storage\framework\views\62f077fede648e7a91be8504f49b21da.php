<?php echo $__env->make('layout.admin-header', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<?php
    $currencySymbol = config('currency.symbol');
?>

<body id="page-top">

    <!-- Page Wrapper -->
    <div id="wrapper">

        <!-- Sidebar -->
        <?php echo $__env->make('layout.admin-sidebar', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        <!-- End of Sidebar -->

        <!-- Content Wrapper -->
        <div id="content-wrapper" class="d-flex flex-column">

            <!-- Main Content -->
            <div id="content">

                <!-- Topbar -->
                <?php echo $__env->make('layout.admin-topbar', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                <!-- End of Topbar -->

                <!-- Begin Page Content -->
                <div class="container-fluid">
                    <?php echo $__env->make('shared.success-message', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                    <?php echo $__env->make('shared.error-message', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">Remittable Payments</h6>
                            <div class="row">
                                <div class="form-group col-md-3">
                                    <select class="form-control" id="to_user_email" name="to_user_email">
                                        <option value="">--Select Remittance Option--</option>
                                        <?php $__currentLoopData = $users; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $user): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <option value="<?php echo e($user->email); ?>"><?php echo e($user->name); ?> -
                                                <?php echo e($user->email); ?></option>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        <option value="bank">Bank Direct</option>
                                    </select>
                                </div>

                                <div class="form-group col-md-3">
                                    <button id="remit-button" class="btn btn-primary">Remit Selected Payments</button>
                                </div>
                            </div>
                            <div class="card-body">

                                
                                <div class="table-responsive">



                                    <table id="myRequests" class="table table-bordered table-striped" width="100%"
                                        cellspacing="0">
                                        <thead>

                                            <tr>
                                                <th>Select</th>
                                                <th>ID</th>
                                                <th>Guest</th>
                                                <th>Room</th>
                                                <th>Payment Method</th>
                                                <th>Amount</th>
                                                <th>Payment Date</th>
                                                <th>Created By</th>
                                                <th>Is Remitted?</th>
                                                <th>Current Location</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php $__currentLoopData = $remittable_payments; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $remittable_payments): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <tr>
                                                    <?php if($remittable_payments->is_remitted == 'no'): ?>
                                                        <td><input type="checkbox" class="payment-checkbox"
                                                                value="<?php echo e($remittable_payments->id); ?>"></td>
                                                    <?php else: ?>
                                                        <td><input type="checkbox" class="payment-checkbox"
                                                                value="<?php echo e($remittable_payments->id); ?>" disabled></td>
                                                    <?php endif; ?>
                                                    <td><?php echo e($remittable_payments->id); ?></td>
                                                    <?php if($remittable_payments->invoice && $remittable_payments->invoice->booking && $remittable_payments->invoice->booking->guest_first_name && $remittable_payments->invoice->booking->guest_last_name): ?>
                                                        <td><?php echo e($remittable_payments->invoice->booking->guest_first_name . ' ' . $remittable_payments->invoice->booking->guest_last_name); ?></td>
                                                    <?php else: ?>
                                                        <td>N/A</td>
                                                    <?php endif; ?>

                                                    <?php if($remittable_payments->invoice && $remittable_payments->invoice->booking && $remittable_payments->invoice->booking->room && $remittable_payments->invoice->booking->room->room_number && $remittable_payments->invoice->booking->room->roomType && $remittable_payments->invoice->booking->room->roomType->name): ?>
                                                        <td><?php echo e($remittable_payments->invoice->booking->room->room_number . '-' . $remittable_payments->invoice->booking->room->roomType->name); ?></td>
                                                    <?php else: ?>
                                                        <td>N/A</td>
                                                    <?php endif; ?>
                                                    <td><?php echo e($remittable_payments->payment_method); ?></td>
                                                    <td style="text-align:right;"><?php echo e($currencySymbol . number_format($remittable_payments->amount,2)); ?></td>
                                                    <td class="checkin-date"
                                                        data-date="<?php echo e($remittable_payments->payment_date); ?>">
                                                    </td>
                                                    <td><?php echo e($remittable_payments->created_by); ?></td>
                                                    <td><?php echo e($remittable_payments->is_remitted); ?></td>
                                                    <td><?php echo e($remittable_payments->current_location); ?></td>
                                                </tr>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </tbody>
                                    </table>

                                    <script src="<?php echo e(asset('assets/js/moment.min.js')); ?>"></script>
                                    <script>
                                        document.addEventListener('DOMContentLoaded', function() {
                                            $('#remit-button').on('click', function() {
                                                let selectedPayments = [];
                                                let from_user_email =
                                                '<?php echo e(get_user_email()); ?>'; // Ensure this function outputs the correct email format

                                                // Get selected payments
                                                $('.payment-checkbox:checked').each(function() {
                                                    selectedPayments.push($(this).val());
                                                });

                                                // Get the email of the user to remit to from the dropdown
                                                let to_user_email = $('#to_user_email').val();

                                                // Initialize totalAmount
                                                let totalAmount = 0;

                                                // Get the amount of all selected payments
                                                $('.payment-checkbox:checked').each(function() {
                                                    let amountText = $(this).closest('tr').find('td:eq(5)').text().trim();
                                                    // Use regular expression to remove all non-numeric characters except the decimal point
                                                    let amount = parseFloat(amountText.replace(/[^0-9.-]+/g, ''));

                                                    // Only add valid numbers
                                                    if (!isNaN(amount)) {
                                                        totalAmount += amount;
                                                    }
                                                });

                                                if (selectedPayments.length === 0) {
                                                    alert('Please select at least one payment to remit.');
                                                    return;
                                                }

                                                // Make the AJAX request
                                                $.ajax({
                                                    url: '<?php echo e(route('admin.remittances.store')); ?>',
                                                    method: 'POST',
                                                    data: {
                                                        _token: '<?php echo e(csrf_token()); ?>',
                                                        payments: selectedPayments,
                                                        from_user_email: from_user_email, // Include the current user's email
                                                        to_user_email: to_user_email,
                                                        amount: totalAmount // Pass the correct total amount
                                                    },
                                                    success: function(response) {
                                                        if (response.success) {
                                                            alert('Payments successfully remitted.');
                                                            location.reload();
                                                        } else {
                                                            alert('Failed to remit payments.');
                                                        }
                                                    },
                                                    error: function(xhr) {
                                                        let errorMessage = 'An error occurred: ' + xhr.statusText;
                                                        if (xhr.responseJSON && xhr.responseJSON.errors) {
                                                            errorMessage = 'Validation Errors: ' + Object.values(xhr
                                                                .responseJSON.errors).flat().join(', ');
                                                        }
                                                        alert(errorMessage);
                                                    }
                                                });
                                            });
                                        });
                                    </script>

                                    
                                    <script>
                                        document.addEventListener('DOMContentLoaded', function() {
                                            // Function to format dates using moment.js
                                            function formatDate(dateString) {
                                                return moment(dateString).format('MMMM D, YYYY h:mm A');
                                            }

                                            // Format all check-in dates
                                            document.querySelectorAll('.checkin-date').forEach(function(element) {
                                                const date = element.getAttribute('data-date');
                                                element.textContent = formatDate(date);
                                            });

                                            // Format all check-out dates
                                            document.querySelectorAll('.checkout-date').forEach(function(element) {
                                                const date = element.getAttribute('data-date');
                                                element.textContent = formatDate(date);
                                            });
                                        });
                                    </script>

                                </div>
                            </div>
                        </div>


                    </div>

                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">All Payments</h6>

                            <div class="card-body">
                                
                                <div class="table-responsive">



                                    <table id="myRequests2" class="table table-bordered table-striped" width="100%"
                                        cellspacing="0">
                                        <thead>
                                            <tr>
                                                <th>ID</th>
                                                <th>Guest</th>
                                                <th>Room</th>
                                                <th>Payment Method</th>
                                                <th>Amount</th>
                                                <th>Payment Date</th>
                                                <th>Created By</th>
                                                <th>Is Remitted?</th>
                                                <th>Current Location</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php $__currentLoopData = $all_payments; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $all_payment): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <tr>

                                                    <td><?php echo e($all_payment->id); ?></td>
                                                    <?php if($all_payment->invoice && $all_payment->invoice->booking && $all_payment->invoice->booking->guest_first_name && $all_payment->invoice->booking->guest_last_name): ?>
                                                        <td><?php echo e($all_payment->invoice->booking->guest_first_name . ' ' . $all_payment->invoice->booking->guest_last_name); ?></td>
                                                    <?php else: ?>
                                                        <td>N/A</td>
                                                    <?php endif; ?>

                                                    <?php if($all_payment->invoice && $all_payment->invoice->booking && $all_payment->invoice->booking->room && $all_payment->invoice->booking->room->room_number && $all_payment->invoice->booking->room->roomType && $all_payment->invoice->booking->room->roomType->name): ?>
                                                        <td><?php echo e($all_payment->invoice->booking->room->room_number . '-' . $all_payment->invoice->booking->room->roomType->name); ?></td>
                                                    <?php else: ?>
                                                        <td>N/A</td>
                                                    <?php endif; ?>
                                                    <td><?php echo e($all_payment->payment_method); ?></td>
                                                    <td style="text-align:right;"><?php echo e($currencySymbol . $all_payment->amount); ?></td>
                                                    <td class="checkin-date"
                                                        data-date="<?php echo e($all_payment->payment_date); ?>">
                                                    </td>
                                                    <td><?php echo e($all_payment->created_by); ?></td>
                                                    <td><?php echo e($all_payment->is_remitted); ?></td>
                                                    <td><?php echo e($all_payment->current_location); ?></td>
                                                </tr>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </tbody>
                                    </table>
                                    <script src="<?php echo e(asset('assets/js/moment.min.js')); ?>"></script>



                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- /.container-fluid -->

            </div>
            <!-- End of Main Content -->

            <!-- Footer -->
            <?php echo $__env->make('layout.admin-footer', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
            <!-- End of Footer -->

        </div>
        <!-- End of Content Wrapper -->

    </div>
    <!-- End of Page Wrapper -->

    <!-- Scroll to Top Button-->
    <a class="scroll-to-top rounded" href="#page-top">
        <i class="fas fa-angle-up"></i>
    </a>

    <!-- Logout Modal-->
    <div class="modal fade" id="logoutModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel"
        aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="exampleModalLabel">Ready to Leave?</h5>
                    <button class="close" type="button" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">×</span>
                    </button>
                </div>
                <div class="modal-body">Select "Logout" below if you are ready to end your current session.</div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" type="button" data-dismiss="modal">Cancel</button>
                    <a class="btn btn-primary" href="login.html">Logout</a>
                </div>
            </div>
        </div>
    </div>

    <?php echo $__env->make('layout.scripts', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

</body>

</html>
<?php /**PATH C:\Users\<USER>\Documents\GitHub\hotel_management_system\resources\views\admin\payments\view-payments.blade.php ENDPATH**/ ?>